import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../screens/home_page.dart';
import '../screens/login_screen.dart';
import '../screens/email_verification_screen.dart';

class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    final auth = AuthService.instance;

    return StreamBuilder<User?>(
      stream: auth.userChanges(),
      builder: (context, snapshot) {
        final user = snapshot.data;

        // Not signed in
        if (user == null) {
          return const LoginScreen();
        }

        // Signed in but email not verified
        if (!user.emailVerified) {
          return const EmailVerificationScreen();
        }

        // Signed in and email verified -> return your app's home screen
        return const MyHomePage(title: 'Numerology App');
      },
    );
  }
}

