# Flutter rules are compiled into the base Flutter SDK since Flutter v1.20.0
# and are applied automatically when building in release mode.

# Add any project-specific rules here.

# Firebase SDK rules
-keep class com.google.firebase.** { *; }
-dontwarn com.google.firebase.**

-keep class com.google.android.gms.** { *; }
-dontwarn com.google.android.gms.**

-keep class com.google.common.** { *; }

# Google Sign-In rules
-keep class com.google.android.gms.auth.api.signin.GoogleSignInOptionsExtension { *; }
-keep class com.google.android.gms.auth.api.signin.internal.zzo { *; }

-dontwarn java.lang.reflect.AnnotatedType