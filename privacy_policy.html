<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - Numerology App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.7;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(0, 0); }
            100% { transform: translate(-20px, -20px); }
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 50px 40px;
        }

        .intro {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9ff;
            border-left: 5px solid #4facfe;
            border-radius: 0 10px 10px 0;
            position: relative;
        }

        .intro::before {
            content: '📋';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section {
            margin-bottom: 35px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .section:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .section h2::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            border-radius: 2px;
        }

        .section p {
            margin-bottom: 15px;
            color: #555;
            text-align: justify;
        }

        .section ul {
            margin: 20px 0;
            padding-left: 0;
            list-style: none;
        }

        .section li {
            margin-bottom: 15px;
            padding: 15px 20px;
            background: #f8f9ff;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
            position: relative;
            transition: all 0.3s ease;
        }

        .section li:hover {
            background: #e8f4fd;
            transform: translateX(5px);
        }

        .section li::before {
            content: '✓';
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            background: #4facfe;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .section li strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 5px;
        }

        .contact-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .contact-section h2 {
            color: white;
            border-bottom-color: rgba(255, 255, 255, 0.3);
        }

        .contact-section h2::after {
            background: white;
        }

        .email {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            border-radius: 50px;
            margin-top: 20px;
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .email:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #f8f9ff;
            color: #666;
            font-size: 0.9rem;
            border-top: 1px solid #e9ecef;
        }

        .last-updated {
            display: inline-block;
            background: #4facfe;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 0.85rem;
            margin-top: 10px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content {
                padding: 30px 20px;
            }

            .section {
                padding: 20px;
                margin-bottom: 25px;
            }

            .intro {
                padding: 20px;
            }

            .section li {
                padding: 12px 15px;
            }
        }

        /* Accessibility improvements */
        .section:focus-within {
            outline: 2px solid #4facfe;
            outline-offset: 2px;
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                color: black;
            }
            
            .container {
                box-shadow: none;
                background: white;
            }
            
            .header {
                background: #f0f0f0 !important;
                color: black !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Privacy Policy</h1>
            <div class="subtitle">Numerology App</div>
        </div>

        <div class="content">
            <div class="intro">
                This Privacy Policy governs the manner in which Numerology App collects, uses, maintains, and discloses information collected from users (each, a "User") of the Numerology mobile application ("App"). This privacy policy applies to the App and all products and services offered by Numerology App.
            </div>

            <div class="section">
                <h2>Personal Identification Information</h2>
                <p>We may collect personal identification information from Users in a variety of ways, including, but not limited to, when Users visit our App, register on the App, fill out a form, and in connection with other activities, services, features or resources we make available on our App. Users may be asked for, as appropriate, name, email address.</p>
                <p>Users may, however, visit our App anonymously. We will collect personal identification information from Users only if they voluntarily submit such information to us. Users can always refuse to supply personally identification information, except that it may prevent them from engaging in certain App related activities.</p>
            </div>

            <div class="section">
                <h2>Non-personal Identification Information</h2>
                <p>We may collect non-personal identification information about Users whenever they interact with our App. Non-personal identification information may include the browser name, the type of computer and technical information about Users means of connection to our App, such as the operating system and the Internet service providers utilized and other similar information.</p>
            </div>

            <div class="section">
                <h2>How We Use Collected Information</h2>
                <p>Numerology App may collect and use Users personal information for the following purposes:</p>
                <ul>
                    <li>
                        <strong>To improve customer service:</strong>
                        Information you provide helps us respond to your customer service requests and support needs more efficiently.
                    </li>
                    <li>
                        <strong>To personalize user experience:</strong>
                        We may use information in the aggregate to understand how our Users as a group use the services and resources provided on our App.
                    </li>
                    <li>
                        <strong>To improve our App:</strong>
                        We may use feedback you provide to improve our products and services.
                    </li>
                    <li>
                        <strong>To send periodic emails:</strong>
                        We may use the email address to send User information and updates pertaining to their order. It may also be used to respond to their inquiries, questions, and/or other requests.
                    </li>
                </ul>
            </div>

            <div class="section">
                <h2>How We Protect Your Information</h2>
                <p>We adopt appropriate data collection, storage and processing practices and security measures to protect against unauthorized access, alteration, disclosure or destruction of your personal information, username, password, transaction information and data stored on our App.</p>
            </div>

            <div class="section">
                <h2>Sharing Your Personal Information</h2>
                <p>We do not sell, trade, or rent Users personal identification information to others. We may share generic aggregated demographic information not linked to any personal identification information regarding visitors and users with our business partners, trusted affiliates and advertisers for the purposes outlined above.</p>
            </div>

            <div class="section">
                <h2>Changes to This Privacy Policy</h2>
                <p>Numerology App has the discretion to update this privacy policy at any time. When we do, we will revise the updated date at the bottom of this page. We encourage Users to frequently check this page for any changes to stay informed about how we are helping to protect the personal information we collect.</p>
                <p>You acknowledge and agree that it is your responsibility to review this privacy policy periodically and become aware of modifications.</p>
            </div>

            <div class="section">
                <h2>Your Acceptance of These Terms</h2>
                <p>By using this App, you signify your acceptance of this policy. If you do not agree to this policy, please do not use our App. Your continued use of the App following the posting of changes to this policy will be deemed your acceptance of those changes.</p>
            </div>

            <div class="section contact-section">
                <h2>Contacting Us</h2>
                <p>If you have any questions about this Privacy Policy, the practices of this App, or your dealings with this App, please contact us at:</p>
                <a href="mailto:<EMAIL>" class="email"><EMAIL></a>
            </div>
        </div>

        <div class="footer">
            <div class="last-updated">Last updated: August 21, 2025</div>
        </div>
    </div>
</body>
</html>