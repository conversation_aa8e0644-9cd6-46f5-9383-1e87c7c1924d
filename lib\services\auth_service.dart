import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'user_service.dart';

class AuthService {
  AuthService._();
  static final AuthService instance = AuthService._();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final UserService _userService = UserService.instance;

  Stream<User?> authStateChanges() => _auth.authStateChanges();

  Stream<User?> userChanges() => _auth.userChanges();

  /// Get current user
  User? get currentUser => _auth.currentUser;

  /// Check if current user's email is verified
  bool get isEmailVerified => _auth.currentUser?.emailVerified ?? false;

  /// Email & password sign in (only for verified users)
  Future<User?> signInWithEmailAndPassword(String email, String password) async {
    try {
      final cred = await _auth.signInWithEmailAndPassword(email: email, password: password);
      final user = cred.user;

      if (user != null && !user.emailVerified) {
        // Sign out the user if email is not verified
        await _auth.signOut();
        throw FirebaseAuthException(
          code: 'email-not-verified',
          message: 'Please verify your email before signing in.',
        );
      }

      return user;
    } catch (e) {
      debugPrint('Sign in error: $e');
      rethrow;
    }
  }

  /// Email & password sign up with user profile creation
  Future<User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
    required String gender,
    required int age,
    required String phoneNumber,
  }) async {
    try {
      final cred = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = cred.user;

      if (user != null) {
        // Send email verification
        await user.sendEmailVerification();

        // Create user profile in Firestore
        await _userService.createUserProfile(
          uid: user.uid,
          email: email,
          fullName: fullName,
          gender: gender,
          age: age,
          phoneNumber: phoneNumber,
          emailVerified: false,
        );

        debugPrint('User created and verification email sent');
      }

      return user;
    } catch (e) {
      debugPrint('Sign up error: $e');
      rethrow;
    }
  }

  /// Send email verification to current user
  Future<void> sendEmailVerification() async {
    final user = _auth.currentUser;
    if (user != null && !user.emailVerified) {
      await user.sendEmailVerification();
      debugPrint('Email verification sent');
    }
  }

  /// Reload current user to check email verification status
  Future<void> reloadUser() async {
    final user = _auth.currentUser;
    if (user != null) {
      await user.reload();
      // Update Firestore if email is now verified
      if (user.emailVerified) {
        await _userService.updateEmailVerificationStatus(user.uid, true);
      }
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }

  /// Sign out from Firebase
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      debugPrint('User signed out successfully');
    } catch (e) {
      debugPrint('Sign out error: $e');
      rethrow;
    }
  }
}